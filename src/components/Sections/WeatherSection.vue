<template>
  <div class="weather-section" :class="{ expanded: isWeatherExpanded }">
    <div class="section-header">
      <span class="section-icon">☀️</span>
      <span class="section-title">天气分析</span>
      <div class="section-actions">
        <button class="section-tts-btn" title="朗读内容" @click="handleTtsClick">
          <img src="@/assets/icon/trumpet.png" alt="朗读" class="section-tts-icon" />
        </button>
        <button class="section-edit-btn" title="编辑" @click="handleWeatherSectionArrowClick">
          <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
        </button>
      </div>
    </div>
    <div class="section-content" :class="{ expanded: isWeatherExpanded }" @click="handleWeatherContentClick">
      <!-- 编辑模式 -->
      <div v-if="showWeatherEdit" class="edit-attribute-container">
        <div class="add-attribute-container">
          <div class="voice-input-wrapper">
            <input
              ref="weatherInput"
              v-model="weatherEditValue"
              type="text"
              class="attribute-value"
              placeholder="请输入当前城市"
              @keydown.enter="handleWeatherEditComplete"
              @keydown.esc="handleWeatherEditCancel"
            />
            <!-- 麦克风按钮在单行输入框右侧 -->
            <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
              <i class="iconfont icon-microphone" class-prefix="icon"></i>
            </div>
          </div>
        </div>
        <div class="edit-actions">
          <button class="save-btn" @click="handleWeatherEditComplete">保存</button>
          <button class="cancel-btn" @click="handleWeatherEditCancel">取消</button>
        </div>
      </div>
      <!-- 显示模式 -->
      <div v-else>
        <!-- 加载中状态 -->
        <div v-if="weatherStatus === 'loading'" class="loading-text">加载中...</div>

        <!-- 没有城市信息状态 -->
        <div v-else-if="weatherStatus === 'no_city'" class="weather-content">
          <div class="weather-suggestion" :class="{ collapsed: !isWeatherExpanded }">
            还没有关于ta的地点信息哦～快去和老董聊聊吧！
          </div>
        </div>

        <!-- API成功响应状态 -->
        <div v-else-if="weatherStatus === 'success'" class="weather-content">
          <!-- 显示当前城市的个性化提醒 -->
          <div v-if="currentCityWeatherData" class="weather-reminder" :class="{ collapsed: !isWeatherExpanded }">
            {{ currentCityWeatherData.personalized_reminder }}
          </div>
          <div v-else class="weather-suggestion" :class="{ collapsed: !isWeatherExpanded }">
            还没有关于ta的地点信息哦～快去和老董聊聊吧！
          </div>
        </div>

        <!-- API错误响应状态 -->
        <div v-else-if="weatherStatus === 'api_error'" class="weather-content">
          <div class="weather-error" :class="{ collapsed: !isWeatherExpanded }">
            <div class="error-title">⚠️ 天气信息获取失败</div>
            <div v-if="weatherData && weatherData.reason" class="error-message">{{ weatherData.reason }}</div>
            <div v-if="weatherData && weatherData.suggestion" class="error-suggestion">
              💡 建议：{{ weatherData.suggestion }}
            </div>
          </div>
        </div>

        <!-- 网络错误状态 -->
        <div v-else-if="weatherStatus === 'network_error'" class="weather-content">
          <div class="weather-error" :class="{ collapsed: !isWeatherExpanded }">
            <div class="error-title">⚠️ 天气信息获取失败</div>
            <div class="error-message">网络请求超时或服务异常</div>
            <div class="error-suggestion">💡 建议：请检查网络连接或稍后重试</div>
          </div>
        </div>

        <!-- 兜底状态 -->
        <div v-else class="weather-content">
          <div class="weather-suggestion" :class="{ collapsed: !isWeatherExpanded }">
            还没有关于ta的地点信息哦～快去和老董聊聊吧！
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue';
import type { IPersonDetail, IGetPersonWeatherResponse } from '@/apis/memory';
import { getPersonWeather } from '@/apis/memory';
import { updatePerson } from '@/apis/relation';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';

// Props定义
interface IProps {
  personDetail: IPersonDetail | null;
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  weatherMicClick: [];
  personUpdated: [personDetail: IPersonDetail];
}>();

// TTS相关
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();
const isTtsPlaying = ref(false);
const ttsId = 'weather-section-tts';

// 天气数据状态枚举
const WEATHER_STATUS = {
  LOADING: 'loading',
  NO_CITY: 'no_city',
  SUCCESS: 'success',
  API_ERROR: 'api_error',
  NETWORK_ERROR: 'network_error',
} as const;

// 响应式数据
const isWeatherExpanded = ref(false);
const showWeatherEdit = ref(false);
const weatherEditValue = ref('');
const loadingWeather = ref(false);
const weatherData = ref<IGetPersonWeatherResponse | null>(null);
const weatherStatus = ref<string>(WEATHER_STATUS.NO_CITY);

// 语音录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 语音录音响应式数据
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const weatherInput = ref<HTMLInputElement>();
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新

// 计算属性：处理后的关键属性
const processedKeyAttributes = computed(() => {
  if (!props.personDetail?.key_attributes) {
    return {};
  }

  const attributes = props.personDetail.key_attributes;
  const processed: Record<string, string> = {};

  Object.entries(attributes).forEach(([key, value]) => {
    if (typeof value === 'string') {
      processed[key] = value;
    } else if (typeof value === 'object' && value !== null) {
      processed[key] = JSON.stringify(value);
    } else {
      processed[key] = String(value);
    }
  });

  return processed;
});

// 计算属性：获取当前城市的天气数据
const currentCityWeatherData = computed(() => {
  if (!weatherData.value || weatherData.value.result !== 'success' || !weatherData.value.weather_data) {
    return null;
  }

  // 查找当前城市的天气数据
  const currentCity = processedKeyAttributes.value['当前城市'];

  if (!currentCity) {
    return null;
  }

  // 在weather_data中查找匹配的城市数据
  const weatherEntries = Object.entries(weatherData.value.weather_data);
  const matchingEntry = weatherEntries.find(
    ([locationKey, cityData]) => locationKey === '当前城市' || cityData.city === currentCity,
  );

  return matchingEntry ? matchingEntry[1] : null;
});

// 切换天气分析展开/收起状态
const toggleWeatherExpanded = () => {
  isWeatherExpanded.value = !isWeatherExpanded.value;
};

// 处理天气分析内容点击事件 - 区分点击展开和复制操作
const handleWeatherContentClick = () => {
  // 如果点击的是编辑模式的input，不触发展开/收起
  if (showWeatherEdit.value) {
    return;
  }

  // 检查是否是文本选择操作（复制操作）
  const selection = window.getSelection();
  if (selection && selection.toString().length > 0) {
    // 有文本被选中，这是复制操作，不触发展开/收起
    return;
  }

  // 否则触发展开/收起
  toggleWeatherExpanded();
};

// 在光标位置插入文字的工具函数（针对input元素）
const insertTextAtCursor = (newText: string) => {
  if (!weatherInput.value) return;

  const inputElement = weatherInput.value;
  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = weatherEditValue.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  weatherEditValue.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 语音录音相关方法
// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 在光标位置插入新文字
        if (textToInsert) {
          insertTextAtCursor(textToInsert);
        }

        lastVoiceText.value = newText;
        voiceMessage.value = newText;
        await autoStopTimeout();
      }
    }
  };
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 两秒不说话自动停止
const autoStopTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 释放麦克风资源
function releaseMicrophoneResources() {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => {
      track.stop();
    });
    mediaStream = null;
    micPermission.value = false;
  }
}

// 取消录音
function cancelRecording() {
  if (recorder) {
    recorder.stop();
  }
  isRecording.value = false;
  voiceMessage.value = '';
  releaseMicrophoneResources();
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次设置 weatherEditValue.value，避免覆盖用户可能的编辑
    console.log('📤 [WeatherSection] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// TTS朗读处理
const handleTtsClick = () => {
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
    isTtsPlaying.value = false;
  } else {
    // 构建朗读内容：读出personalized_reminder
    const ttsContent =
      currentCityWeatherData.value?.personalized_reminder || '还没有关于ta的地点信息哦～快去和老董聊聊吧！';

    if (ttsContent.trim()) {
      isTtsPlaying.value = true;
      play({
        id: ttsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(ttsId) && audioStatus.value === 'completed') {
          isTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

// 处理天气分析section-arrow点击事件
const handleWeatherSectionArrowClick = () => {
  showWeatherEdit.value = true;
  weatherEditValue.value = processedKeyAttributes.value['当前城市'] || '';

  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const input = document.querySelector('.weather-section .attribute-value') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
};

// 处理天气分析编辑完成
const handleWeatherEditComplete = async () => {
  if (!props.personDetail) return;

  try {
    const aliases = props.personDetail.aliases || '';
    const submitAliases = aliases === '' ? '' : aliases;

    // 更新key_attributes中的当前城市
    const updatedKeyAttributes = { ...(props.personDetail.key_attributes as Record<string, string>) };
    if (weatherEditValue.value.trim()) {
      updatedKeyAttributes['当前城市'] = weatherEditValue.value.trim();
    } else {
      delete updatedKeyAttributes['当前城市'];
    }

    const response = await updatePerson(props.personDetail.person_id, {
      user_id: props.userId,
      canonical_name: props.personDetail.canonical_name,
      aliases: submitAliases,
      relationships: props.personDetail.relationships as string[],
      profile_summary: props.personDetail.profile_summary,
      key_attributes: updatedKeyAttributes,
      is_user: props.personDetail.is_user,
      avatar: props.personDetail.avatar,
    });

    if (response && response.result === 'success') {
      // 更新本地数据
      const updatedPersonDetail = {
        ...props.personDetail,
        key_attributes: updatedKeyAttributes,
      };
      emit('personUpdated', updatedPersonDetail);
      showSuccessToast('当前城市更新成功');

      // 重新加载天气数据
      void loadWeatherData();
      // 保存成功后退出编辑模式
      showWeatherEdit.value = false;
    } else {
      showFailToast('当前城市更新失败');
    }
  } catch (error) {
    console.error('更新当前城市失败:', error);
    showFailToast('当前城市更新失败');
  }
};

// 处理天气分析编辑取消
const handleWeatherEditCancel = () => {
  showWeatherEdit.value = false;
  // 恢复原始值
  weatherEditValue.value = processedKeyAttributes.value['当前城市'] || '';
};

// 加载天气数据
const loadWeatherData = async () => {
  if (!props.personDetail) {
    weatherStatus.value = WEATHER_STATUS.NO_CITY;
    weatherData.value = null;
    return;
  }

  // 检查是否有当前城市信息
  if (!processedKeyAttributes.value['当前城市']) {
    weatherStatus.value = WEATHER_STATUS.NO_CITY;
    weatherData.value = null;
    return;
  }

  try {
    weatherStatus.value = WEATHER_STATUS.LOADING;
    loadingWeather.value = true;
    console.log('🔄 [WeatherSection] 开始获取天气数据...');

    const response = await getPersonWeather({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [WeatherSection] 天气数据响应:', response);
    weatherData.value = response;

    // 根据API响应设置状态
    if (response.result === 'success') {
      weatherStatus.value = WEATHER_STATUS.SUCCESS;
    } else if (response.result === 'error') {
      weatherStatus.value = WEATHER_STATUS.API_ERROR;
    } else {
      weatherStatus.value = WEATHER_STATUS.NETWORK_ERROR;
    }
  } catch (error) {
    console.error('❌ [WeatherSection] 获取天气数据失败:', error);
    weatherStatus.value = WEATHER_STATUS.NETWORK_ERROR;
    weatherData.value = null;
  } finally {
    loadingWeather.value = false;
  }
};

// 监听personDetail变化，重新加载天气数据
watch(
  () => props.personDetail,
  (newPersonDetail) => {
    if (newPersonDetail) {
      void loadWeatherData();
    }
  },
  { immediate: true },
);

// 组件挂载时加载天气数据
onMounted(() => {
  void loadWeatherData();
});

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
.weather-section {
  border: none;
  border-radius: 16px;
  padding: 22px;
  margin-top: 24px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

// 天气分析展开/收起控制
.weather-section .section-content {
  cursor: pointer;
  transition: max-height 0.3s ease;

  &:not(.expanded) {
    max-height: 200px;
    overflow: hidden;
  }

  &.expanded {
    max-height: none;
    overflow: visible;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: var(--person-detail-title); // 使用PersonDetail专用标题颜色
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .section-edit-btn,
  .section-mic-btn,
  .section-tts-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    .section-edit-icon,
    .section-mic-icon,
    .section-tts-icon {
      width: 18px;
      height: 18px;
      filter: var(--icon-filter-primary);
    }
  }
}

.section-content {
  color: var(--person-detail-context); // 使用PersonDetail专用内容颜色
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: var(--person-detail-context);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

// 天气分析内容样式
.weather-content {
  .weather-reminder,
  .weather-suggestion,
  .weather-error {
    font-size: 32px;
    line-height: 1.6;
    transition: all 0.3s ease;

    &.collapsed {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .weather-error {
    .error-title,
    .error-message,
    .error-suggestion {
      margin-bottom: 8px;
    }
  }

  .weather-suggestion {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0 0 16px 0;
    font-size: 32px;
    line-height: 1.6;
    color: var(--person-detail-context);
  }

  .weather-reminder {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0 0 16px 0;
    font-size: 32px;
    line-height: 1.6;
    color: var(--person-detail-context);
  }
}

.edit-attribute-container {
  .add-attribute-container {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: center;
    width: 100%;
    box-sizing: border-box;

    .voice-input-wrapper {
      position: relative;
      width: 100%;

      .voice-toggle-inner {
        position: absolute;
        right: 16px;
        top: 40px; // 与第一行文字中心对齐
        transform: translateY(-50%);
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-accent);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 24px;
          color: var(--text-primary);
        }
      }

      .attribute-value {
        flex: 1;
        min-width: 0;
        max-width: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 14px 76px 14px 16px; // 右侧留出更多空间给更大的语音按钮
        color: var(--person-detail-context);
        font-size: 32px;
        box-sizing: border-box;
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
          border-color: rgba(255, 255, 255, 0.5);
          background: rgba(255, 255, 255, 0.15);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;

    .save-btn,
    .cancel-btn {
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid;

      &.save-btn {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;

        &:hover {
          background: var(--primary-color-strong);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px var(--primary-color-medium);
        }
      }

      &.cancel-btn {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

@keyframes breathing {
  0% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--primary-color);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 0 8px var(--primary-color-medium);
  }
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--primary-color);
  }
}
</style>
